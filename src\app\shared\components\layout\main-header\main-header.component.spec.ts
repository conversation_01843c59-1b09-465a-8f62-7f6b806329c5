import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { of } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { OverlayModule } from '@angular/cdk/overlay';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Org } from '@shared/models/user-profile.model';

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['changeRole', 'hasSomeRole', 'isSuperUser']);
		mockRouter = jasmine.createSpyObj('Router', ['navigate', 'createUrlTree', 'serializeUrl'], {
			events: of(new NavigationEnd(1, 'test', 'test')),
		});

		await TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot(), MatIconModule, MatTooltipModule, OverlayModule],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useClass: class ActivatedRouteMock {} },
				{ provide: UserProfileService, useValue: mockUserProfileService },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call changeRole on switch', () => {
		mockUserProfileService.changeRole.and.returnValue(of(false));

		fixture.detectChanges();

		component.onSwitch({ id: '1', name: 'test' } as Org);
		expect(mockUserProfileService.changeRole).toHaveBeenCalledWith('1');
	});

	it('should not reload when changeRole returns false', () => {
		mockUserProfileService.changeRole.and.returnValue(of(false));

		fixture.detectChanges();

		component.onSwitch({ id: '1', name: 'test' } as Org);
		expect(mockUserProfileService.changeRole).toHaveBeenCalledWith('1');
	});
});
