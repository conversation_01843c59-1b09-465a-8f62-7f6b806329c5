import { ChangeDetectionStrategy, Component, NO_ERRORS_SCHEMA, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { filter } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OverlayModule } from '@angular/cdk/overlay';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { MatDividerModule } from '@angular/material/divider';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Org } from '@shared/models/user-profile.model';
import { AsyncPipe, CommonModule } from '@angular/common';
import { UserRole } from '@shared/models/user-role.model';

@Component({
	selector: 'iata-main-header',
	templateUrl: './main-header.component.html',
	styleUrls: ['./main-header.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatTooltipModule,
		MatDividerModule,
		RouterLink,
		RouterLinkActive,
		TranslateModule,
		OverlayModule,
		CommonModule,
		AsyncPipe,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class MainHeaderComponent extends RolesAwareComponent implements OnInit {
	isOpen = false;

	readonly menuSliRoles: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
	readonly menuHawbRoles: string[] = [UserRole.FORWARDER, UserRole.CARRIER];
	readonly menuMawbRoles: string[] = [UserRole.FORWARDER, UserRole.CARRIER];

	constructor(
		private readonly router: Router,
		private readonly userProfileService: UserProfileService
	) {
		super();
	}

	ngOnInit(): void {
		this.router.events
			.pipe(
				filter((event) => event instanceof NavigationEnd),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();
	}

	onSwitch(role: Org): void {
		this.userProfileService.changeRole(role.id).subscribe((data) => {
			if (data) window.location.reload();
		});
	}
}
