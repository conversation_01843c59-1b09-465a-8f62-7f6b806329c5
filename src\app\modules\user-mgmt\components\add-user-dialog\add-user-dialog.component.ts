import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	CUSTOM_ELEMENTS_SCHEMA,
	Inject,
	Input,
	OnInit,
	ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Organization } from '@shared/models/organization.model';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Observable, startWith } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CodeName } from '@shared/models/code-name.model';
import { USER_TYPES } from '../../ref-data/user-list.data';
import { SecondaryUser } from '../../models/secondary-user.model';
import { SecondaryUserPanelComponent } from '../secondary-user-panel/secondary-user-panel.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { UserListObject } from '../../models/user-list-object.model';

@Component({
	selector: 'orll-add-user-dialog',
	templateUrl: './add-user-dialog.component.html',
	styleUrl: './add-user-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatButtonModule,
		MatDialogModule,
		MatIconModule,
		MatInputModule,
		MatSelectModule,
		MatCheckboxModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		CommonModule,
		MatAutocompleteModule,
		SecondaryUserPanelComponent,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AddUserDialogComponent extends DestroyRefComponent implements OnInit {
	@Input() isSecondary = false;

	orgList: Organization[] = [];
	filteredOrgList: Organization[] = [];
	filteredPrimaryOrgList: Organization[] = [];
	userTypes: CodeName[] = USER_TYPES;
	secondaryUserList: SecondaryUser[] = [];

	addUserForm: FormGroup = new FormGroup({
		firstName: new FormControl<string>('', [Validators.required]),
		lastName: new FormControl<string>('', [Validators.required]),
		email: new FormControl<string>('', [Validators.required, Validators.email]),
		orgName: new FormControl<string>('', [Validators.required]),
		primaryOrgId: new FormControl<string>('', [Validators.required]),
		userType: new FormControl<string>('', [Validators.required]),
	});

	@ViewChild(SecondaryUserPanelComponent) secondaryUserPanel: SecondaryUserPanelComponent | undefined;

	constructor(
		public readonly orgMgmtRequestService: OrgMgmtRequestService,
		public readonly userMgmtRequestService: UserMgmtRequestService,
		private readonly cdr: ChangeDetectorRef,
		public dialogRef: MatDialogRef<AddUserDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
	}

	private fillUserInfo(userId: string): void {
		this.userMgmtRequestService
			.getUserInfo(userId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					res.userType = res.userType.toString();
					this.addUserForm.patchValue(res);
					if (res.secondaryOrgIds?.length) {
						this.isSecondary = true;
						this.secondaryUserList = res.secondaryOrgIds;
					}
					this.cdr.markForCheck();
				},
			});
	}

	private initRefData(): void {
		this.orgMgmtRequestService
			.getOrgList()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((res) => {
				this.orgList = res;
				if (this.data.userId) this.fillUserInfo(this.data.userId);
				this.setupAutocomplete();
				this.cdr.markForCheck();
			});
	}

	private setupAutocomplete(): void {
		this.addUserForm
			.get('primaryOrgId')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPrimaryOrgList = this.filterOrgs(search);
			});
	}

	filterOrgs(search: string): Organization[] {
		return this.orgList.filter((org) => org.name.toLowerCase().includes(search.toLowerCase().trim()));
	}

	displayOrgName = (id: string): string => {
		const org = this.orgList.find((item) => item.id === id);
		return org?.name ?? '';
	};

	getSecondaryUserList(): SecondaryUser[] {
		return this.secondaryUserPanel?.getSecondaryUserList() ?? [];
	}

	private userDetailRequest(userPayload: UserListObject): Observable<string> {
		if (!this.data.userId) {
			return this.userMgmtRequestService.createUser(userPayload);
		} else {
			return this.userMgmtRequestService.updateUser({ ...userPayload, userId: this.data.userId });
		}
	}

	onOk(): void {
		this.addUserForm.markAllAsTouched();
		if (this.addUserForm.invalid) {
			return;
		}

		const userPayload: UserListObject = {
			...this.addUserForm.value,
			secondaryOrgIds: this.getSecondaryUserList(),
		};

		this.userDetailRequest(userPayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(() => {
				this.cdr.markForCheck();
				this.dialogRef.close();
			});
	}

	onCancel(): void {
		this.dialogRef.close();
	}
}
