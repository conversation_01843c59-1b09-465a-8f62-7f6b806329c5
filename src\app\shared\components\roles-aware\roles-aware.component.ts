import { Directive, inject } from '@angular/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { UserRole } from '@shared/models/user-role.model';
import { UserProfile } from '@shared/models/user-profile.model';
import { Observable, of } from 'rxjs';

@Directive()
export abstract class RolesAwareComponent extends DestroyRefComponent {
	readonly userRoles = UserRole;
	protected profileService = inject(UserProfileService);

	hasSomeRole(roles: string[]): Observable<boolean> {
		return this.profileService ? this.profileService.hasSomeRole(roles) : of(false);
	}

	hasPermission(permission: string, module: string): Observable<boolean> {
		return this.profileService ? this.profileService.hasPermission(permission, module) : of(false);
	}

	getCurrentUser(): Observable<UserProfile | null> {
		return this.profileService.currentUser$;
	}

	isSuperUser(): Observable<boolean> {
		return of(this.profileService.isSuperUser());
	}
}
