import { importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { AppComponent } from './app/app.component';
import { DEFAULT_LANG } from '@shared/global.constants';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { provideTranslateService, TranslateLoader } from '@ngx-translate/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { bootstrapApplication, BrowserModule } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideRouter, withComponentInputBinding, withHashLocation } from '@angular/router';
import { ROUTES } from './app/app-routes.constant';
import { MAT_RADIO_DEFAULT_OPTIONS } from '@angular/material/radio';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { MsalInterceptor, MsalModule, MsalService } from '@azure/msal-angular';
import { MSAL_CLIENT_CONFIG, MSAL_GUARD_CONFIG, MSAL_INTERCEPTOR_CONFIG } from '@shared/auth/msal-auth.config';

bootstrapApplication(AppComponent, {
	providers: [
		importProvidersFrom(BrowserModule, MsalModule.forRoot(MSAL_CLIENT_CONFIG, MSAL_GUARD_CONFIG, MSAL_INTERCEPTOR_CONFIG)),
		MsalService,
		{
			provide: HTTP_INTERCEPTORS,
			useClass: MsalInterceptor,
			multi: true,
		},

		{
			provide: HTTP_INTERCEPTORS,
			useClass: BusinessErrorInterceptor,
			multi: true,
		},

		{
			provide: MAT_RADIO_DEFAULT_OPTIONS,
			useValue: { color: 'primary' },
		},
		{
			provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
			useValue: {
				appearance: 'outline',
				floatLabel: 'always',
			},
		},

		provideTranslateService({
			loader: {
				provide: TranslateLoader,
				useFactory: (http: HttpClient) => new TranslateHttpLoader(http),
				deps: [HttpClient],
			},
			defaultLanguage: DEFAULT_LANG,
		}),
		provideZoneChangeDetection({ eventCoalescing: true }),
		provideAnimationsAsync(),
		provideRouter(ROUTES, withHashLocation(), withComponentInputBinding()),
		provideAnimations(),
		provideHttpClient(withInterceptorsFromDi()),
	],
}).catch((err) => console.error(err));
