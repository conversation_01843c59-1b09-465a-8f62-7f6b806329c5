import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { MawbTableComponent } from './mawb-table.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { MawbListObject } from '../../models/mawb-list-object.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MatTableDataSource } from '@angular/material/table';
import { By } from '@angular/platform-browser';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';

describe('MawbTableComponent', () => {
	let component: MawbTableComponent;
	let fixture: ComponentFixture<MawbTableComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	const mockRecords: MawbListObject[] = [
		{
			mawbId: '1',
			mawbNumber: '123-456789',
			airlineCode: 'AA',
			goodsDescription: 'Electronics',
			origin: 'JFK',
			destination: 'LAX',
			pieceQuantity: '10',
			latestStatus: 'Created',
			createDate: '2023-01-01',
			orgId: 'org1',
		},
		{
			mawbId: '2',
			mawbNumber: '987-654321',
			airlineCode: 'DL',
			goodsDescription: 'Clothing',
			origin: 'SFO',
			destination: 'ORD',
			pieceQuantity: '20',
			latestStatus: 'Shipped',
			createDate: '2023-01-02',
			orgId: 'org2',
		},
	];

	const mockPageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	beforeEach(async () => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [MawbTableComponent],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbTableComponent);
		component = fixture.componentInstance;

		component.records = mockRecords;
		component.dataSource = new MatTableDataSource<MawbListObject>(mockRecords);
		component.totalRecords = 2;
		component.pageParams = mockPageParams;

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize dataSource with all fields', () => {
		expect(component.dataSource.data).toEqual(mockRecords);
		expect(component.dataSource.data[0].pieceQuantity).toBe('10');
		expect(component.dataSource.data[0].latestStatus).toBe('Created');
		expect(component.dataSource.data[0].orgId).toBe('org1');
	});

	it('should update dataSource when records input changes', () => {
		const newRecords = [
			...mockRecords,
			{
				mawbId: '3',
				mawbNumber: '111-222333',
				airlineCode: 'UA',
				goodsDescription: 'Books',
				origin: 'LHR',
				destination: 'CDG',
				pieceQuantity: '30',
				latestStatus: 'Delivered',
				createDate: '2023-01-03',
				orgId: 'org3',
			},
		];

		component.records = newRecords;
		component.ngOnChanges({
			records: {
				currentValue: newRecords,
				previousValue: mockRecords,
				firstChange: false,
				isFirstChange: () => false,
			},
		});

		fixture.detectChanges();
		expect(component.dataSource.data.length).toBe(3);
		expect(component.dataSource.data[2].pieceQuantity).toBe('30');
	});

	it('should emit sortChange event with correct field', () => {
		spyOn(component.sortChange, 'emit');
		const sortEvent: Sort = { active: 'pieceQuantity', direction: 'asc' };

		component.onSortChange(sortEvent);

		expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		expect(component.currentSort.active).toBe('pieceQuantity');
	});

	it('should emit pagination event with sort and pagination data', fakeAsync(() => {
		spyOn(component.pagination, 'emit');
		const pageEvent: PageEvent = {
			pageIndex: 1,
			pageSize: 50,
			length: 100,
		};

		component.currentSort = { active: 'latestStatus', direction: 'asc' };
		// @ts-expect-error private
		component.emitPaginationWithSort(pageEvent);
		tick();

		expect(component.pagination.emit).toHaveBeenCalledWith({
			...pageEvent,
			sortField: 'latestStatus',
			sortDirection: 'asc',
		});
	}));

	it('should emit shareHawb event with full record', () => {
		spyOn(component.shareMawb, 'emit');
		const mockRecord = mockRecords[0];

		component.shareMawb.emit(mockRecord);

		expect(component.shareMawb.emit).toHaveBeenCalledWith(mockRecord);
		expect(mockRecord.mawbNumber).toBe('123-456789');
	});

	it('should have unique trackBy identifier', () => {
		const mockRecord = mockRecords[0];
		const result = component.trackByMawbId(0, mockRecord);
		expect(result).toBe(`${mockRecord.mawbId}${mockRecord.createDate}`);
	});

	it('should render all fields in table rows', () => {
		const rows = fixture.debugElement.queryAll(By.css('tbody tr'));

		const firstRowCells = rows[0].queryAll(By.css('td'));
		expect(firstRowCells[0].nativeElement.textContent).toContain('123-456789');
		expect(firstRowCells[1].nativeElement.textContent).toContain('AA');
		expect(firstRowCells[2].nativeElement.textContent).toContain('Electronics');
		expect(firstRowCells[3].nativeElement.textContent).toContain('JFK');
		expect(firstRowCells[4].nativeElement.textContent).toContain('LAX');
		expect(firstRowCells[5].nativeElement.textContent).toContain('Created');
		expect(firstRowCells[6].nativeElement.textContent).toContain('2023-01-01');

		expect(firstRowCells[7]).toBeTruthy();
	});
});
